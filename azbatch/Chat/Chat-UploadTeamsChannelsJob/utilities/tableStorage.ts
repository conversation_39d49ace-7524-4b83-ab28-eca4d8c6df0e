import { TableClient } from "@azure/data-tables";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const AZURE_STORAGE_CONNECTION_STRING = process.env["AZURE_STORAGE_CONNECTION_STRING"] ?? "";
const AZURE_STORAGE_TEAMSCHATS_TABLE_NAME = process.env["AZURE_STORAGE_TEAMSCHATS_TABLE_NAME"] ?? "TeamsChats";

function getClient(
  logger: CustomLogger,
  tableName: string
): TableClient {
  try {
    logger.log("[TableStorage:getClient] Initializing Client Connection...");
    
    const connectionString = AZURE_STORAGE_CONNECTION_STRING;
    if (!connectionString) {
      throw new Error("[TableStorage:getClient] AZURE_STORAGE_CONNECTION_STRING must be defined");
    }

    const tableClient = TableClient.fromConnectionString(connectionString, tableName);
    logger.log("[TableStorage:getClient] Client Initialized Successfully");
    
    return tableClient;
  } catch (error) {
    logger.log(`[TableStorage:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateTableStorageConnection(
    logger: CustomLogger
): Promise<void> {
  try {
    logger.log("[TableStorage:validateConnection] Testing Table Storage Connection...");
    
    if (!AZURE_STORAGE_TEAMSCHATS_TABLE_NAME) {
      throw new Error("AZURE_STORAGE_TEAMSCHATS_TABLE_NAME is required");
    }
    
    const client = getClient(logger, AZURE_STORAGE_TEAMSCHATS_TABLE_NAME);
    const iterator = client.listEntities();
    await iterator.next();

    logger.log("[TableStorage:validateConnection] Table Storage Connection Successful");
  } catch (error) {
      logger.log(`[TableStorage:validateConnection] Table Storage Connection Failed: ${error}`);
    throw error;
  }
}

export async function fetchUsersChatsData(
    logger: CustomLogger
): Promise<any[]> {
  try {
    logger.log("[TableStorage:fetchUsersChatsData] Fetching Users Chats in Storage Table...");
    
    if (!AZURE_STORAGE_TEAMSCHATS_TABLE_NAME) {
      throw new Error("AZURE_STORAGE_TEAMSCHATS_TABLE_NAME is required");
    }
    
    const client = getClient(logger, AZURE_STORAGE_TEAMSCHATS_TABLE_NAME);
    
    const entities = [];
    for await (const entity of client.listEntities()) {
      entities.push(entity);
    }
    
    logger.log(`[TableStorage:fetchUsersChatsData] Found: ${entities.length} Users Chats`);
    return entities;
  } catch (error) {
    logger.log(`[TableStorage:fetchUsersChatsData] Failed to Fetch Users Chats: ${error}`);
    throw error;
  }
}

export async function updateChatsStatus(
  logger: CustomLogger,
  userId: string,
  chatId: string
): Promise<void> {
  try {
    logger.log(`[TableStorage:updateChatsStatus] Updating status for userId: ${userId} and chatId: ${chatId}`);
    const client = getClient(logger, AZURE_STORAGE_TEAMSCHATS_TABLE_NAME);
    const entity = await client.getEntity(userId, chatId);
    const updatedEntity = {
      partitionKey: userId,
      rowKey: chatId,
      lastCheckedDatetime: new Date().toISOString(),
      etag: entity.etag
    };
    
    await client.updateEntity(updatedEntity, "Merge");
    // logger.log(`[TableStorage:updateChatsStatus] Successfully Updated Status`);
    
  } catch (error: any) {
    if (error?.statusCode === 404) {
      logger.log(`[TableStorage:updateChatsStatus] Entity Not Found for userId: ${userId} and chatId: ${chatId}`);
    } else {
      logger.log(`[TableStorage:updateChatsStatus] Failed to Update: ${error}`);
      throw error;
    }
  }
}

export async function updateTableStorageChatsStatus(
  logger: CustomLogger,
  chatEntry: { chatId: string; members: any[] },
): Promise<void> {
  try {
    logger.log(`[TableStorage:updateTableStorageChatsStatus] Updating Status for chat: ${chatEntry.chatId} with ${chatEntry.members.length} members`);
    
    const updatePromises = chatEntry.members.map(member => 
      updateChatsStatus(logger, member.userId, chatEntry.chatId)
    );
    
    await Promise.allSettled(updatePromises);
    logger.log(`[TableStorage:updateTableStorageChatsStatus] Completed Status Updates for Chat: ${chatEntry.chatId}`);
    
  } catch (error) {
    logger.log(`[TableStorage:updateTableStorageChatsStatus] Error Updating Status: ${error}`);
    throw error;
  }
}