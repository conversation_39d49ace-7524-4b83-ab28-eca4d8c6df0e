import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import { 
  createTeams1on1GroupChatsRequests,
  createTeams1on1GroupChatsMessagesRequests,
  createChatsMembersRequests 
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeamsChats
} from '../utilities/graph';
import { logLongArray, CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChats,
  ITeamsUniqueChats,
  ITeams1on1GroupMessages,
  ITeamsChatsMembers
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertTeams1on1GroupChatsMessages,
  updateChatsMembers
} from '../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_1on1GROUP_CHATS_MEMBERS_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_1on1GROUP_CHATS_MEMBERS_BATCH_COUNTS ?? '20');
const MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE ?? '50');
const MAX_TEAMS_1on1GROUP_CHATS_MEMBERS_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_1on1GROUP_CHATS_MEMBERS_CHUNK_SIZE ?? '50');

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  logger: Logger,
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);
  logLongArray(logger, '[Impl:fetchTargetUsers] AllGroupUsers.id', 800, users.map((u) => u.id));

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processTeams1on1GroupChats(
  logger: Logger,
  client: Client,
  targetUsersData: IBatchResponseData[],
): Promise<ITeamsUniqueChats[]> {

  logger.log(`[Impl:processTeams1on1GroupChats] Total targetUsersData to Process: ${targetUsersData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChats] targetUsersData: ${JSON.stringify(targetUsersData)}`); // !!!

  const userTeams1on1GroupBatchRequestsCreated: BatchRequestData[] = targetUsersData
    .filter(data => data.id)
    .flatMap((data) =>
      createTeams1on1GroupChatsRequests(data?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeams1on1GroupChats] Total GraphAPI - userTeams1on1GroupBatchRequestsCreated: ${userTeams1on1GroupBatchRequestsCreated.length} | MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS: ${MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS}`);
  const userSplitTeams1on1GroupChatsBatchRequests = splitArrayIntoChunks(userTeams1on1GroupBatchRequestsCreated, MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS);
  logger.log(`[Impl:processTeams1on1GroupChats] Total Teams1on1GroupChats Split Batch: ${userSplitTeams1on1GroupChatsBatchRequests.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChats] userSplitTeams1on1GroupChatsBatchRequests: ${JSON.stringify(userSplitTeams1on1GroupChatsBatchRequests)}`); // !!!

  const uniqueChatsMap = new Map<string, ITeamsUniqueChats>();

  // Process all batch requests
  const totalTeams1on1GroupChatsRequests = userSplitTeams1on1GroupChatsBatchRequests.length;
  for (let i = 0; i < totalTeams1on1GroupChatsRequests; i++) {
    try {
      const currentTeams1on1GroupChatsBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_1on1GROUP_CHATS: (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests}) ====`);
      const currentUserSplitTeams1on1GroupChatsBatchRequests = userSplitTeams1on1GroupChatsBatchRequests[i];
      logger.log(`[Impl:processTeams1on1GroupChats] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeams1on1GroupChats = await fetchJsonBatchForTeamsChats(logger, client, currentUserSplitTeams1on1GroupChatsBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeams1on1GroupChats] *** batchResultTeams1on1GroupChats == ${JSON.stringify(batchResultTeams1on1GroupChats)} === (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests})`); // !!! 2
      
      if (!batchResultTeams1on1GroupChats.responses || batchResultTeams1on1GroupChats.responses.length === 0) {
        logger.log(`[Impl:processTeams1on1GroupChats] No Responses in Batch === (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests})`);
        continue;
      }
      
      // Extract the Unique Chats
      for (const response of batchResultTeams1on1GroupChats.responses) {
        if (response.status !== 200 || !response.body?.value) {
          logger.log(`[Impl:processTeams1on1GroupChats] Skipping ${response.id} due to invalid status or missing body value.`);
          continue;
        }
        for (const chatDetail of response.body.value) {
          const chatIdVal =(chatDetail as ITeamsChats).id;
          const topicVal = (chatDetail as ITeamsChats).topic;
          const chatTypeVal = (chatDetail as ITeamsChats).chatType;
          // Get chatId, topic, chatType
          if (chatIdVal && !uniqueChatsMap.has(chatIdVal)) {
            uniqueChatsMap.set(chatIdVal, {
              chatId: chatIdVal,
              topic: topicVal,
              chatType: chatTypeVal
            });
          }
        }
      }

      // Clear batch responses AFTER processing ALL users
      batchResultTeams1on1GroupChats.responses = [];
      // Free memory for this batch
      userSplitTeams1on1GroupChatsBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_1on1GROUP_CHATS: (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests}) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeams1on1GroupChats] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
  
  const uniqueChats = Array.from(uniqueChatsMap.values());
  userSplitTeams1on1GroupChatsBatchRequests.length = 0;

  return uniqueChats;
}
/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processTeams1on1GroupChatsMessages(
  logger: Logger,
  client: Client,
  uniqueChatsData: ITeamsUniqueChats[],
): Promise<void> {

  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total uniqueChatsData to Process: ${uniqueChatsData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMessages] uniqueChatsData: ${JSON.stringify(uniqueChatsData)}`);  // !!!

  const usersTeams1on1GroupChatsMessagesBatchRequestsCreated: BatchRequestData[] = uniqueChatsData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createTeams1on1GroupChatsMessagesRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total GraphAPI - usersTeams1on1GroupChatsMessagesBatchRequestsCreated: ${usersTeams1on1GroupChatsMessagesBatchRequestsCreated.length} | MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS: ${MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS}`);
  const userSplitTeams1on1GroupChatsMessagesBatchRequests = splitArrayIntoChunks(usersTeams1on1GroupChatsMessagesBatchRequestsCreated, MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total Teams1on1GroupChatsMessages Split Batch: ${userSplitTeams1on1GroupChatsMessagesBatchRequests.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMessages] userSplitTeams1on1GroupChatsMessagesBatchRequests: ${JSON.stringify(userSplitTeams1on1GroupChatsMessagesBatchRequests)}`);  // !!!

  // Process all batch requests
  const totalTeams1on1GroupChatsMessagesBatchRequests = userSplitTeams1on1GroupChatsMessagesBatchRequests.length;
  for (let i = 0; i < totalTeams1on1GroupChatsMessagesBatchRequests; i++) {
    try {
      const currentTeams1on1GroupChatsMessagesBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ====`);
      const currentUserSplitTeams1on1GroupChatsMessagesBatchRequests = userSplitTeams1on1GroupChatsMessagesBatchRequests[i];
      logger.log(`[Impl:processTeams1on1GroupChatsMessages] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeams1on1GroupChatsMessages = await fetchJsonBatchForTeamsChats(logger, client, currentUserSplitTeams1on1GroupChatsMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeams1on1GroupChatsMessages] *** batchResultTeams1on1GroupChatsMessages == ${JSON.stringify(batchResultTeams1on1GroupChatsMessages)} === (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`); // !!! 2

      if (!batchResultTeams1on1GroupChatsMessages.responses || batchResultTeams1on1GroupChatsMessages.responses.length === 0) {
        logger.log(`[Impl:processTeams1on1GroupChatsMessages] No Responses in Batch === (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }

      await processBatchResultTeams1on1GroupChatsMessages(logger, batchResultTeams1on1GroupChatsMessages, currentTeams1on1GroupChatsMessagesBatchRequests, totalTeams1on1GroupChatsMessagesBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultTeams1on1GroupChatsMessages.responses = [];
      // Free memory for this batch
      userSplitTeams1on1GroupChatsMessagesBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeams1on1GroupChatsMessages] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
}

async function processBatchResultTeams1on1GroupChatsMessages(
  logger: Logger,
  batchResultTeams1on1GroupChatsMessages: IBatchResponses,
  currentTeams1on1GroupChatsMessagesBatchRequests: number,
  totalTeams1on1GroupChatsMessagesBatchRequests: number
): Promise<void> {

  const totalBatchResultTeams1on1GroupChatsMessages = batchResultTeams1on1GroupChatsMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultTeams1on1GroupChatsMessages; j++) {
    const currentBatchResultTeams1on1GroupChatsMessages = j + 1;
    const batchResultTeams1on1GroupChatsMessagesResponses = (batchResultTeams1on1GroupChatsMessages.responses ?? [])[j];
    const chatId = batchResultTeams1on1GroupChatsMessagesResponses.id;
    
    logger.log(`\n+=+= START - PROCESSING BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) +=+=`);

    if (!batchResultTeams1on1GroupChatsMessagesResponses || batchResultTeams1on1GroupChatsMessagesResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Skipping chatId: ${chatId} with Error Data: ${batchResultTeams1on1GroupChatsMessagesResponses?.status} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
      continue;
    }

    const singleResultTeams1on1GroupChatsMessages = { responses: [batchResultTeams1on1GroupChatsMessagesResponses] };
    // logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] singleResultTeams1on1GroupChatsMessages == ${JSON.stringify(singleResultTeams1on1GroupChatsMessages)} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);  // !!!
                                                                      
    const modifiedTeams1on1GroupChatsMessagesChunk = processSingleResultTeams1on1GroupChatsMessages([singleResultTeams1on1GroupChatsMessages], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] *** modifiedTeams1on1GroupChatsMessagesChunk == ${JSON.stringify(modifiedTeams1on1GroupChatsMessagesChunk)} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`); // !!!

    const totalModifiedTeams1on1GroupChatsMessageChunk = modifiedTeams1on1GroupChatsMessagesChunk.length;
    // Insert each messageChunk
    for (let k = 0; k < totalModifiedTeams1on1GroupChatsMessageChunk; k++) {
      const messageChunk = modifiedTeams1on1GroupChatsMessagesChunk[k];
      const currentModifiedTeams1on1GroupChatsMessages = k + 1;
      if (!messageChunk) {
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Skipping Undefined Teams1on1GroupChatsMessages at Index: ${k} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }
      logger.log(`---- START - INSERT COSMOS_DB | TEAMS_1on1GROUP_CHATS_MESSAGES: (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Inserting Teams1on1GroupChatsMessages...`);
        await insertTeams1on1GroupChatsMessages(logger, [messageChunk]);
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Successfully Inserted Teams1on1GroupChatsMessages...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Failed Inserting: ${error} (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }
      modifiedTeams1on1GroupChatsMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - INSERT COSMOS_DB | TEAMS_1on1GROUP_CHATS_MESSAGES: (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ----`);
    }

    modifiedTeams1on1GroupChatsMessagesChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`+=+= END - PROCESSING BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) +=+=`);
  }
}

function processSingleResultTeams1on1GroupChatsMessages(
  singleResTeams1on1GroupChatsMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResTeams1on1GroupChatsMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] Skipping Invalid Teams1on1GroupChatsMessages Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalTeams1on1GroupChatsMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] Total Teams1on1GroupChatsMessages in response.body.value: ${totalTeams1on1GroupChatsMessages}`);

      const chatId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeams1on1GroupMessages[];
      const transformedMessages = allTeamsChatMessages.map((item) => {
        const hasAttachments = 
          'attachments' in item && 
          Array.isArray((item as any).attachments) && 
          (item as any).attachments.length > 0;
        return {
          ...item,
          hasAttachments
        };
      });
      //logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] transformedMessages: ${JSON.stringify(transformedMessages)}`); // !!!
      const filteredMessages = transformedMessages.filter((message) => {
        return message.messageType !== 'systemEventMessage' && 
               message.messageType !== 'unknownFutureValue';
      });
      // logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] filteredMessages: ${JSON.stringify(filteredMessages)}`);  // !!!
      const chunkSize = MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE;

      // Process messages in chunks
      for (let i = 0; i < filteredMessages.length; i += chunkSize) {
        const teams1on1GroupChatsMessagesAfterChunk = filteredMessages.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultTeams1on1GroupChatsMessages] teams1on1GroupChatsMessagesAfterChunk: ${JSON.stringify(teams1on1GroupChatsMessagesAfterChunk)}`); // !!!
        processTeams1on1GroupChatsMessagesChunk(chatId, teams1on1GroupChatsMessagesAfterChunk, allProcessedData, logger);
        teams1on1GroupChatsMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }

  // logger.info(`[Impl:processSingleResultTeams1on1GroupChatsMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processTeams1on1GroupChatsMessagesChunk(
  chatId: string,
  teams1on1GroupChatsMessagesAfterChunk: ITeams1on1GroupMessages[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeams1on1GroupMessages[] = [];

  for (const item of teams1on1GroupChatsMessagesAfterChunk) {
    if (hasEmptyRequiredFields(item, logger)) {
      continue;
    }
    processedValues.push(createEncryptedMessage(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: chatId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeams1on1GroupChatsMessagesChunk] Successfully Modified Teams1on1GroupChatsMessages ${processedValues.length} Teams1on1GroupChatsMessages Inside Chunk | MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE: ${MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: ITeams1on1GroupMessages, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyId = !item.id || isEmpty(item.id);
  const hasEmptyBody = !item.body || isEmpty(item.body.content);
  const hasEmptyValues = hasEmptyId || hasEmptyBody;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipping Message: ${item.id} with Empty Required Fields`);
    return true;
  }
  return false;
}

function createEncryptedMessage(item: ITeams1on1GroupMessages): ITeams1on1GroupMessages {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? encrypt(item.replyToId) : null,
    messageType: item.messageType ? encrypt(item.messageType) : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: encrypt(item.from.application.displayName)
      } : null,
      device: item.from.device ? {
        displayName: encrypt(item.from.device.displayName)
      } : null,
      user: item.from.user ? {
        displayName: encrypt(item.from.user.displayName)
      } : null,

    } : null,
    body: item.body ? {
      content: encrypt(item.body.content)
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: encrypt(item.channelIdentity.channelId)
    } : null,
  };
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/
/******************************************************* ========== PHASE 6 - START ========== *******************************************************/
export async function processTeams1on1GroupChatsMembers(
  logger: Logger,
  client: Client,
  uniqueChatsData: Array<{chatId: string, topic: string, chatType: string}>,
  targetUsersData: IBatchResponseData[],
): Promise<void> {

  logger.log(`[Impl:processTeams1on1GroupChatsMembers] Total uniqueChatsData to Process: ${uniqueChatsData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMembers] uniqueChatsData: ${JSON.stringify(uniqueChatsData)}`);  // !!!
  // logger.log(`[Impl:processTeams1on1GroupChatsMembers] targetUsersData: ${JSON.stringify(targetUsersData)}`);  // !!!

  const userChatsMembersBatchRequestsCreated: BatchRequestData[] = uniqueChatsData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createChatsMembersRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeams1on1GroupChatsMembers] Total GraphAPI - userChatsMembersBatchRequestsCreated: ${userChatsMembersBatchRequestsCreated.length} | MAX_GRAPH_API_1on1GROUP_CHATS_MEMBERS_BATCH_COUNTS: ${MAX_GRAPH_API_1on1GROUP_CHATS_MEMBERS_BATCH_COUNTS}`);
  const userSplitChatsMembersBatchRequests = splitArrayIntoChunks(userChatsMembersBatchRequestsCreated, MAX_GRAPH_API_1on1GROUP_CHATS_MEMBERS_BATCH_COUNTS);
  logger.log(`[Impl:processTeams1on1GroupChatsMembers] Total TeamsChannel Split Batch: ${userSplitChatsMembersBatchRequests.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMembers] userSplitChatsMembersBatchRequests: ${JSON.stringify(userSplitChatsMembersBatchRequests)}`);  // !!!

  // Process all batch requests
  const totalChatsMembersBatchRequests = userSplitChatsMembersBatchRequests.length;
  for (let i = 0; i < totalChatsMembersBatchRequests; i++) {
    try {
      const currentChatsMembersBatchRequests = i + 1;

      logger.log( `\n==== START - BATCH | CHATS_MEMBERS: (Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) ====`);
      const currentUserSplitChatsMembersBatchRequests = userSplitChatsMembersBatchRequests[i];
      logger.log(`[Impl:processTeams1on1GroupChatsMembers] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultChatsMembers = await fetchJsonBatchForTeamsChats(logger, client, currentUserSplitChatsMembersBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeams1on1GroupChatsMembers] *** batchResultChatsMembers == ${JSON.stringify(batchResultChatsMembers)} === (Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);  // !!!

      if (!batchResultChatsMembers.responses || batchResultChatsMembers.responses.length === 0) {
        logger.log(`[Impl:processTeams1on1GroupChatsMembers] No Responses in Batch === (Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);
        continue;
      }
      // Create Filtered version of batchResultChatsMembers
      const filteredBatchResultChatsMembers = {
        responses: (batchResultChatsMembers.responses ?? []).map(response => {
          const filteredResponse = { ...response };
          // Compare the batchResultChatsMembers "Teams Members" with targetUsersData "Microsotf 365 Members"
          if (filteredResponse.body?.value && Array.isArray(filteredResponse.body.value)) {
            const filteredMembers = filteredResponse.body.value.filter(member => 
              targetUsersData.some(user => user.id === (member as { userId: string }).userId)
            );
            const uniqueUserIds = new Set();
            const uniqueMembers = filteredMembers.filter(member => {
              const userId = (member as { userId: string }).userId;
              if (uniqueUserIds.has(userId)) {
                return false;
              } else {
                uniqueUserIds.add(userId);
                return true;
              }
            });
            filteredResponse.body = {
              ...filteredResponse.body,
              value: uniqueMembers
            };
          }
          return filteredResponse;
        })
      };
      // logger.log(`[Impl:processTeams1on1GroupChatsMembers] *** filtered_batchResultChatsMembers == ${JSON.stringify(filteredBatchResultChatsMembers)}`); // !!!

      await processBatchResultChatsMembers(logger, filteredBatchResultChatsMembers, currentChatsMembersBatchRequests, totalChatsMembersBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultChatsMembers.responses = [];
      // Free memory for this batch
      userSplitChatsMembersBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | CHATS_MEMBERS: (Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeams1on1GroupChatsMembers] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
}

async function processBatchResultChatsMembers(
  logger: Logger,
  filteredBatchResultChatsMembers: IBatchResponses,
  currentChatsMembersBatchRequests: number,
  totalChatsMembersBatchRequests: number
): Promise<void> {

  const totalBatchResultChatsMembers = filteredBatchResultChatsMembers.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChatsMembers; j++) {
    const currentBatchResultChatsMembers = j + 1;
    const batchResultChatsMembersResponses = (filteredBatchResultChatsMembers.responses ?? [])[j];
    const teamId = batchResultChatsMembersResponses.id;

    logger.log(`\n+=+= START - PROCESSING BATCH | CHATS_MEMBERS: (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) +=+=`);
 
    if (!batchResultChatsMembersResponses || batchResultChatsMembersResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChatsMembers] Skipping teamId: ${teamId} with Error Data: ${batchResultChatsMembersResponses?.status} === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);
      continue;
    }

    const singleResultChatsMembers = { responses: [batchResultChatsMembersResponses] };
    // logger.info(`[Impl:processBatchResultChatsMembers] singleResultChatsMembers == ${JSON.stringify(singleResultChatsMembers)} === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);  // !!!

    const modifiedChatsMembersChunk = processSingleResultChatsMembers([singleResultChatsMembers], logger);
    // *** View Raw Response
    // logger.info(`[Impl:processBatchResultChatsMembers] *** modifiedChatsMembersChunk == ${JSON.stringify(modifiedChatsMembersChunk)} === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);  // !!!

    const totalModifiedChatsMembersChunk = modifiedChatsMembersChunk.length;

    // Update each membersChunk
    for (let k = 0; k < totalModifiedChatsMembersChunk; k++) {
      const membersChunk = modifiedChatsMembersChunk[k];
      const currentModifiedMembersChunk = k + 1;
      if (!membersChunk) {
        logger.info(`[Impl:processBatchResultChatsMembers] Skipping Undefined Teams1on1GroupChatsMessages at Index: ${k} === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);
        continue;
      }
      logger.log(`---- START - UPDATE COSMOS_DB | CHATS_MEMBERS: (ChatsMembers Chunk: ${currentModifiedMembersChunk} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} ChatsMembers Inside) === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultChatsMembers] Updating ChatsMembers...`);
        await updateChatsMembers(logger, [membersChunk]);
        logger.info(`[Impl:processBatchResultChatsMembers] Successfully Updated ChatsMembers...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChatsMembers] Failed Updating: ${error} (Chunk: ${currentModifiedMembersChunk} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} ChatsMembers Inside) === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);
        continue;
      }
      modifiedChatsMembersChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - UPDATE COSMOS_DB | CHATS_MEMBERS: (Chunk: ${currentModifiedMembersChunk} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} ChatsMembers Inside) === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) ----`);
    }

    modifiedChatsMembersChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`+=+= END - PROCESSING BATCH | CHATS_MEMBERS: (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests}) +=+=`);
  }
}

function processSingleResultChatsMembers(
  singleResultChatsMembers: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChatsMembers) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChatsMembers] Skipping Invalid ChatsMembers Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalChatsMembers = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChatsMembers] Total ChatsMembers in response.body.value: ${totalChatsMembers}`);

      const teamsId = response.id ?? '';
      const allChatsMembers = response.body?.value as ITeamsChatsMembers[];
      // logger.log(`[Impl:processSingleResultChatsMembers] allChatsMembers: ${JSON.stringify(allChatsMembers)}`); // !!!
      const chunkSize = MAX_TEAMS_1on1GROUP_CHATS_MEMBERS_CHUNK_SIZE;

      // Process members in chunks
      for (let i = 0; i < allChatsMembers.length; i += chunkSize) {
        const chatMembersAfterChunk = allChatsMembers.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultChatsMembers] chatMembersAfterChunk: ${JSON.stringify(chatMembersAfterChunk)}`); // !!!
        processTeams1on1GroupChatsMembersChunk(teamsId, chatMembersAfterChunk, allProcessedData, logger);
        chatMembersAfterChunk.length = 0;
      }
      allChatsMembers.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultChatsMembers] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processTeams1on1GroupChatsMembersChunk(
  teamsId: string,
  chatMembersAfterChunk: ITeamsChatsMembers[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChatsMembers[] = [];

  for (const item of chatMembersAfterChunk) {
    if (hasEmptyRequiredFieldsMembers(item, logger)) {
      continue;
    }
    processedValues.push(createFieldMembers(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);
    logger.info(`[Impl:processTeams1on1GroupChatsMembersChunk] Successfully Modified ChatsMembers: ${processedValues.length} ChatsMembers Inside Chunk | MAX_TEAMS_1on1GROUP_CHATS_MEMBERS_CHUNK_SIZE: ${MAX_TEAMS_1on1GROUP_CHATS_MEMBERS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMembers(item: ITeamsChatsMembers, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyId = !item.id || isEmpty(item.id);
  const hasEmptyValues = hasEmptyId;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFieldsMembers] Skipping message with empty required fields. Message ID: ${item.id}`);
    return true;
  }
  return false;
}

function createFieldMembers(item: ITeamsChatsMembers): ITeamsChatsMembers {
  return {
    id: item.id,
    displayName: item.displayName,
    visibleHistoryStartDateTime: item.visibleHistoryStartDateTime,
    userId: item.userId,
    email: item.email
  };
}
/******************************************************* ========== PHASE 6 - END ========== *********************************************************/