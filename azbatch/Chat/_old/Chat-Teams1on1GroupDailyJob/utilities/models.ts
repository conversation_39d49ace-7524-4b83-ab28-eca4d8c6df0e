import { Entity, NullableOption, PublicError } from '@microsoft/microsoft-graph-types/microsoft-graph';

export type ValueOf<T> = T[keyof T];

/**
 * Graph API Batchリクエストのレスポンス1件分
 */
export interface IBatchResponseData {
  id?: string,
  status?: number,
  headers?: NullableOption<{ 'Retry-After'?: string, 'Content-Type'?: string }>
  body?: {
    value?: Entity[],
    error?: NullableOption<PublicError>,
  },
}

/**
 * Graph API Batchリクエストのレスポンス
 * Ref: BatchResponseBody
 */
export interface IBatchResponses {
  "@odata.nextLink"?: string;
  responses?: IBatchResponseData[],
}

/**
 * Teamsのアクティビティフィード設定
 */
export interface ITeamsActivityNotificationConfig {
  chainId?: number,
  topic?: {
    source?: string;
    value?: string,
  },
  activityType?: string;
  previewText?: {
    content?: string;
    contentType?: string;
  },
  templateParameters?: { [key: string]: string }[],
}

/**
 * Azure Functionsが渡すタイマーオブジェクト
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=javascript
 */
export interface TimerObject {
  schedule: unknown;
  scheduleStatus: {
    last?: string;
    lastUpdated?: string;
    next?: string;
  },
  isPastDate: boolean;
}

/**
 * Teams Chats
 */
export interface ITeamsChats {
  id: string;
  topic: string;
  chatType: string;
}
/**
 * Teams Unique Chats
 */
export interface ITeamsUniqueChats {
  chatId: string;
  topic: string;
  chatType: string;
}
/**
 * Teams Chat Messages
 */
export interface ITeams1on1GroupMessages {
  security_user_id: string[];
  id: string;
  kind: string;
  replyToId: string | null;
  messageType: string | null;
  createdDateTime: string | null;
  lastModifiedDateTime: string | null;
  chatId: string | null;
  from: IMessageFrom | null;
  body: IMessageBody | null;
  hasAttachments: boolean;
  channelIdentity: IChannelIdentity | null;
}
export interface IMessageFrom {
  application: IMessageApplication | null;
  device: IMessageDevice | null;
  user: IMessageUser | null;
}
export interface IMessageApplication {
  displayName: string;
}
export interface IMessageDevice {
  displayName: string;
}
export interface IMessageUser {
  displayName: string;
}
export interface IMessageBody {
  content: string;
}
export interface IChannelIdentity {
  teamId: string ;
  channelId: string;
}
/**
 * Teams Chat Members
 */
export interface ITeamsChatsMembers {
  id: string;
  displayName: string;
  visibleHistoryStartDateTime: string | null;
  userId: string | null;
  email: string | null;
}