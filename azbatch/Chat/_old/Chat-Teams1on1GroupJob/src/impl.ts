import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import { 
  createUserChatRequests,
  createUserUniqueChatMessagesRequests,
  createChatsMembersRequests 
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForChats
} from '../utilities/graph';
import { CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChats,
  ITeamsUniqueChats,
  ITeamsUniqueChatsMessages,
  ITeamsChatsMembers
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertUniqueChatsMessages,
  updateChatsMembers
} from '../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const CHAT_MAX_GRAPH_API_CHATS_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_GRAPH_API_CHATS_BATCH_COUNTS ?? '20');
const CHAT_MAX_GRAPH_API_UNIQUECHATS_MESSAGES_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_GRAPH_API_UNIQUECHATS_MESSAGES_BATCH_COUNTS ?? '20');
const CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS ?? '20');
const CHAT_MAX_UNIQUECHATS_MESSAGES_CHUNK_SIZE = parseInt(process.env.CHAT_MAX_UNIQUECHATS_MESSAGES_CHUNK_SIZE ?? '50');
const CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE = parseInt(process.env.CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE ?? '50');

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processUsersBatch(
  logger: Logger,
  client: Client,
  userBatch: IBatchResponseData[],
): Promise<void> {

  logger.log(`[Impl:processUsersBatch] Total userBatch to Process: ${userBatch.length}`);
  
  const CHAT_MAX_USER_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_USER_BATCH_COUNTS ?? '20');
  const splitUserBatch = splitArrayIntoChunks(userBatch, CHAT_MAX_USER_BATCH_COUNTS);
  
  logger.log(`[Impl:processUsersBatch] Total User Batches: ${splitUserBatch.length} | CHAT_MAX_USER_BATCH_COUNTS: ${CHAT_MAX_USER_BATCH_COUNTS}`);

  for (let batchIndex = 0; batchIndex < splitUserBatch.length; batchIndex++) {
    const currentUserBatch = splitUserBatch[batchIndex];
    const currentUserBatchNumber = batchIndex + 1;

    logger.log(`\n===== 🚀 1. START BATCH = 🙍 USERS BATCH: ${currentUserBatchNumber} of ${splitUserBatch.length} with ${currentUserBatch.length} USERS INSIDE ===== `);

    try {
      await processUserChatsBatch(logger, client, currentUserBatch, currentUserBatchNumber, splitUserBatch.length);
      splitUserBatch[batchIndex] = [];
      if (global.gc) {
        global.gc();
      }
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      logger.error(`[Impl:processUsersBatch] Error Processing User Batch ${currentUserBatchNumber}: ${error}`);
      continue;
    }

    logger.log(`===== 🚀 1. END BATCH = 🙍 USERS BATCH: ${currentUserBatchNumber} of ${splitUserBatch.length} with ${currentUserBatch.length} USERS INSIDE =====\n`);
  }
  splitUserBatch.length = 0;
  logger.log(`[Impl:processUsersBatch] Completed Processing All User Batches.`);
}

// incudes 1on1 and group chats
async function processUserChatsBatch(
  logger: Logger,
  client: Client,
  userBatch: IBatchResponseData[],
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {

  const chatsBatchRequestsCreated: BatchRequestData[] = userBatch
    .filter(data => data.id)
    .flatMap((data) =>
      createUserChatRequests(data?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processUserChatsBatch] Created ${chatsBatchRequestsCreated.length} Chats Requests for ${userBatch.length} Users`);
 
  const splitChatsBatchRequests = splitArrayIntoChunks(chatsBatchRequestsCreated, CHAT_MAX_GRAPH_API_CHATS_BATCH_COUNTS);
  logger.log(`[Impl:processUserChatsBatch] Total Split Chats Batch: ${splitChatsBatchRequests.length} | CHAT_MAX_GRAPH_API_CHATS_BATCH_COUNTS: ${CHAT_MAX_GRAPH_API_CHATS_BATCH_COUNTS}`);
  logger.log(`[Impl:processUserChatsBatch] splitChatsBatchRequests: ${JSON.stringify(splitChatsBatchRequests)}`); // !!!

  const uniqueChatsMap = new Map<string, ITeamsUniqueChats>();

  const totalChatsBatchRequests = splitChatsBatchRequests.length;
  for (let i = 0; i < totalChatsBatchRequests; i++) {
    try {
      const currentChatsBatchRequests = i + 1;

      // CHATS
      logger.log(`
        -----------------------------
        CHATS PROCESSING
        -----------------------------
      `);

      logger.log(`\n----- 🚀 2.CHATS START BATCH = 📡 API REQUEST - CHATS: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----`);

      const currentSplitChatsBatchRequests = splitChatsBatchRequests[i];
      // logger.log(`[Impl:processUserChatsBatch] splitChatsBatchRequests_inside == ${JSON.stringify(splitChatsBatchRequests)}`); // !!!
      // logger.log(`[Impl:processUserChatsBatch] currentSplitChatsBatchRequests == ${JSON.stringify(currentSplitChatsBatchRequests)}`); // !!!

      const batchResultChats = await fetchJsonBatchForChats(logger, client, currentSplitChatsBatchRequests);
      // // *** View Raw Response
      // logger.log(`*** [Impl:processUserChatsBatch] batchResultMails == ${JSON.stringify(batchResultChats)}); // !!!

      if (!batchResultChats.responses || batchResultChats.responses.length === 0) {
        logger.log(`[Impl:processUserChatsBatch] No Responses in API REQUEST - CHATS`);
        continue;
      }
      
      // Extract the Unique Chats
      for (const response of batchResultChats.responses) {
        if (response.status !== 200 || !response.body?.value) {
          logger.log(`[Impl:processUserChatsBatch] Skipping ${response.id} due to invalid status or missing body value.`);
          continue;
        }
        for (const chatDetail of response.body.value) {
          const chatIdVal =(chatDetail as ITeamsChats).id;
          const topicVal = (chatDetail as ITeamsChats).topic;
          const chatTypeVal = (chatDetail as ITeamsChats).chatType;
          // Get chatId, topic, chatType
          if (chatIdVal && !uniqueChatsMap.has(chatIdVal)) {
            uniqueChatsMap.set(chatIdVal, {
              chatId: chatIdVal,
              topic: topicVal,
              chatType: chatTypeVal
            });
          }
        }
      }

      const uniqueChats = Array.from(uniqueChatsMap.values());
      splitChatsBatchRequests.length = 0;
      // logger.log(`[Impl:processUserChatsBatch]: Unique Chats: ${JSON.stringify(uniqueChats)}\n`); // !!!

      await processUserUniqueChatsMessages(logger, client, uniqueChats, currentChatsBatchRequests, totalChatsBatchRequests, currentUserBatchNumber, totalUserBatch);

      batchResultChats.responses = [];
      splitChatsBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`----- 🚀 2.CHATS END BATCH = 📡 API REQUEST - CHATS: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----\n`);

      // CHAT MEMBERS
      logger.log(`
        -----------------------------
        CHAT MEMBERS PROCESSING
        -----------------------------
      `);

      logger.log(`----- 🚀 2.MEMBERS START - GETTING CHAT MEMBERS | 📡 API REQUEST - CHATS: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} with ${uniqueChats.length} UNIQUE CHATS | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----`);
      
      await processUserChatsMembers(logger, client, uniqueChats, userBatch, currentUserBatchNumber, totalUserBatch);
      
      logger.log(`----- 🚀 2.MEMBERS START - GETTING CHAT MEMBERS | 📡 API REQUEST - CHATS: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} with ${uniqueChats.length} UNIQUE CHATS | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----`);
      
    } catch (error) {
      logger.error(`[Impl:processUserChatsBatch] Error Processing API REQUEST - CHATS: ${i + 1}: ${error}`);
      continue;
    }
  }
  chatsBatchRequestsCreated.length = 0;
  splitChatsBatchRequests.length = 0;
}

async function processUserUniqueChatsMessages(
  logger: Logger,
  client: Client,
  uniqueChatsData: ITeamsUniqueChats[],
  currentChatsBatchRequests: number,
  totalChatsBatchRequests: number,
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {

  logger.log(`[Impl:processUserUniqueChatsMessages] Total Unique Chats to Process: ${uniqueChatsData.length}`);

  const uniqueChatMessagesBatchRequestsCreated: BatchRequestData[] = uniqueChatsData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createUserUniqueChatMessagesRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processUserUniqueChatsMessages] Created ${uniqueChatMessagesBatchRequestsCreated.length} UniqueChats Requests`);
 
  const splitUniqueChatsMessagesBatchRequests = splitArrayIntoChunks(uniqueChatMessagesBatchRequestsCreated, CHAT_MAX_GRAPH_API_UNIQUECHATS_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processUserUniqueChatsMessages] Total Split UniqueChats Messages Batch: ${splitUniqueChatsMessagesBatchRequests.length} | CHAT_MAX_GRAPH_API_UNIQUECHATS_MESSAGES_BATCH_COUNTS: ${CHAT_MAX_GRAPH_API_UNIQUECHATS_MESSAGES_BATCH_COUNTS}`);
  logger.log(`[Impl:processUserUniqueChatsMessages] splitUniqueChatsMessagesBatchRequests: ${JSON.stringify(splitUniqueChatsMessagesBatchRequests)}`); // !!!

  const totalUniqueChatsMessagesBatchRequests = splitUniqueChatsMessagesBatchRequests.length;
  for (let i = 0; i < totalUniqueChatsMessagesBatchRequests; i++) {
    const currentUniqueChatsMessagesBatchRequests = i + 1;

    logger.log(`\n----- 🚀 3. START BATCH = 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----`);
    
    try {
      const currentSplitUniqueChatsMessagesBatchRequests = splitUniqueChatsMessagesBatchRequests[i];
      // logger.log(`[Impl:processUserUniqueChatsMessages] splitUniqueChatsMessagesBatchRequestst_inside == ${JSON.stringify(splitUniqueChatsMessagesBatchRequests)}`); // !!!
      // logger.log(`[Impl:processUserUniqueChatsMessages] currentSplitUniqueChatsMessagesBatchRequests == ${JSON.stringify(currentSplitUniqueChatsMessagesBatchRequests)}`); // !!!

      const batchResultUniqueChatsMessages = await fetchJsonBatchForChats(logger, client, currentSplitUniqueChatsMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`*** [Impl:processUserUniqueChatsMessages] batchResultUniqueChatsMessages == ${JSON.stringify(batchResultUniqueChatsMessages)}`); // !!!

      if (!batchResultUniqueChatsMessages.responses || batchResultUniqueChatsMessages.responses.length === 0) {
        logger.log(`[Impl:processUserUniqueChatsMessages] No Responses in API REQUEST - UNIQUE_CHATS_MESSAGE`);
        continue;
      }

      await processBatchResultUniqueChatsMessages(logger, batchResultUniqueChatsMessages, currentUniqueChatsMessagesBatchRequests, totalUniqueChatsMessagesBatchRequests, currentChatsBatchRequests, totalChatsBatchRequests, currentUserBatchNumber, totalUserBatch);

      batchResultUniqueChatsMessages.responses = [];
      splitUniqueChatsMessagesBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processUserUniqueChatsMessages] Error Processing API REQUEST - UNIQUE_CHATS_MESSAGES ${i + 1}: ${error}`);
      continue;
    }

    logger.log(`----- 🚀 3. END BATCH = 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} -----`);
    
  }
  uniqueChatMessagesBatchRequestsCreated.length = 0;
  splitUniqueChatsMessagesBatchRequests.length = 0;
}

async function processBatchResultUniqueChatsMessages(
  logger: Logger,
  batchResultUniqueChatsMessages: IBatchResponses,
  currentUniqueChatsMessagesBatchRequests: number,
  totalUniqueChatsMessagesBatchRequests: number,
  currentChatsBatchRequests: number,
  totalChatsBatchRequests: number,
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {

  const totalBatchResultUniqueChatsMessages = batchResultUniqueChatsMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultUniqueChatsMessages; j++) {
    const currentBatchResultUniqueChatsMessages = j + 1;
    const batchResultUniqueChatsMessagesResponses = (batchResultUniqueChatsMessages.responses ?? [])[j];
    const chatsId = batchResultUniqueChatsMessagesResponses.id;

    logger.log(`\n\n***** 🚀 4. START BATCH = 🔄 PROCESSSING RESULT - UNIQUE_CHATS_MESSAGES: chatsId: ${chatsId} - ${currentBatchResultUniqueChatsMessages} of ${totalBatchResultUniqueChatsMessages} | 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} *****`);

    if (!batchResultUniqueChatsMessagesResponses || batchResultUniqueChatsMessagesResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultUniqueChatsMessages] Skipping chatsId: ${chatsId} with Error Data: ${batchResultUniqueChatsMessagesResponses?.status}`);
      continue;
    }

    const singleResultUniqueChatsMessages = { responses: [batchResultUniqueChatsMessagesResponses] };
    // logger.info(`[Impl:processBatchResultUniqueChatsMessages] singleResultUniqueChatsMessages == ${JSON.stringify(singleResultUniqueChatsMessages)}`);  // !!!
                                                                      
    const modifiedUniqueChatsMessagesChunk = processSingleResultUniqueChatsMessages([singleResultUniqueChatsMessages], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultUniqueChatsMessages] *** modifiedUniqueChatsMessagesChunk == ${JSON.stringify(modifiedUniqueChatsMessagesChunk)}`); // !!!

    // Inserting Start...
    const totalModifiedUniqueChatsMessageChunk = modifiedUniqueChatsMessagesChunk.length;
    for (let k = 0; k < totalModifiedUniqueChatsMessageChunk; k++) {
      const messagesChunk = modifiedUniqueChatsMessagesChunk[k];
      const currentModifiedUniqueChatsMessages = k + 1;
      if (!messagesChunk) {
        logger.info(`[Impl:processBatchResultUniqueChatsMessages] Skipping Undefined UniqueChatsMessages at Index: ${k}`);
        continue;
      }
      logger.log(`\n+++++ 🚀 5. START BATCH = ➕ INSERTING UNIQUE_CHATS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedUniqueChatsMessages} of ${totalModifiedUniqueChatsMessageChunk} with ${messagesChunk.body?.value?.length} UniqueChatsMessages Inside / 🔄 PROCESSSING RESULT - UNIQUE_CHATS_MESSAGES: chatsId: ${chatsId} - ${currentBatchResultUniqueChatsMessages} of ${totalBatchResultUniqueChatsMessages} | 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} +++++`);
      try {
        logger.info(`[Impl:processBatchResultUniqueChatsMessages] Inserting UniqueChatsMessages...`);
        await insertUniqueChatsMessages(logger, [messagesChunk]);
        logger.info(`[Impl:processBatchResultUniqueChatsMessages] Successfully Inserted UniqueChatsMessages...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultUniqueChatsMessages] Failed Inserting: ${error}`);
        continue;
      }
      modifiedUniqueChatsMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ 🚀 5. END BATCH = ➕ INSERTING UNIQUE_CHATS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedUniqueChatsMessages} of ${totalModifiedUniqueChatsMessageChunk} with ${messagesChunk.body?.value?.length} UniqueChatsMessages Inside / 🔄 PROCESSSING RESULT - UNIQUE_CHATS_MESSAGES: chatsId: ${chatsId} - ${currentBatchResultUniqueChatsMessages} of ${totalBatchResultUniqueChatsMessages} | 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} +++++`);
    }
    // Inserting End...

    modifiedUniqueChatsMessagesChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));
    logger.log(`***** 🚀 4. END BATCH = 🔄 PROCESSSING RESULT - UNIQUE_CHATS_MESSAGES: chatsId: ${chatsId} - ${currentBatchResultUniqueChatsMessages} of ${totalBatchResultUniqueChatsMessages} | 📡 API REQUEST - UNIQUE_CHATS_MESSAGES: ${currentUniqueChatsMessagesBatchRequests} of ${totalUniqueChatsMessagesBatchRequests} | 📡 API REQUEST - CHAT: ${currentChatsBatchRequests} of ${totalChatsBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch} *****`);
  }
}

function processSingleResultUniqueChatsMessages(
  singleResultUniqueChatsMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultUniqueChatsMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultUniqueChatsMessages] Skipping Invalid UniqueChatsMessages Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalUniqueChatsMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultUniqueChatsMessages] Total UniqueChatsMessages in response.body.value: ${totalUniqueChatsMessages}`);

      const chatId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeamsUniqueChatsMessages[];
      const transformedMessages = allTeamsChatMessages.map((item) => {
        const hasAttachments = 
          'attachments' in item && 
          Array.isArray((item as any).attachments) && 
          (item as any).attachments.length > 0;
        return {
          ...item,
          hasAttachments
        };
      });
      //logger.log(`[Impl:processSingleResultUniqueChatsMessages] transformedMessages: ${JSON.stringify(transformedMessages)}`); // !!!
      const filteredMessages = transformedMessages.filter((message) => {
        return message.messageType !== 'systemEventMessage' && 
               message.messageType !== 'unknownFutureValue';
      });
      // logger.log(`[Impl:processSingleResultUniqueChatsMessages] filteredMessages: ${JSON.stringify(filteredMessages)}`);  // !!!
      const chunkSize = CHAT_MAX_UNIQUECHATS_MESSAGES_CHUNK_SIZE;

      // Process messages in chunks
      for (let i = 0; i < filteredMessages.length; i += chunkSize) {
        const uniqueChatsMessagesAfterChunk = filteredMessages.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultUniqueChatsMessages] uniqueChatsMessagesAfterChunk: ${JSON.stringify(uniqueChatsMessagesAfterChunk)}`); // !!!
        processUserUniqueChatsMessagesChunk(chatId, uniqueChatsMessagesAfterChunk, allProcessedData, logger);
        uniqueChatsMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultUniqueChatsMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processUserUniqueChatsMessagesChunk(
  chatId: string,
  uniqueChatsMessagesAfterChunk: ITeamsUniqueChatsMessages[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsUniqueChatsMessages[] = [];
  let skippedCount = 0;
  for (const item of uniqueChatsMessagesAfterChunk) {
    if (hasEmptyRequiredFields(item)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createEncryptedMessage(item));
  }

  // Log the skipped count if any items were skipped
  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} UniqueChatsMessages with Null Values in this chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: chatId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processUserUniqueChatsMessagesChunk] Successfully Modified: ${processedValues.length} UniqueChatsMessages Inside Chunk | CHAT_MAX_UNIQUECHATS_MESSAGES_CHUNK_SIZE: ${CHAT_MAX_UNIQUECHATS_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: ITeamsUniqueChatsMessages): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.id) ||
         isEmpty(item.body?.content)
}

// function createEncryptedMessage(item: ITeamsUniqueChatsMessages): ITeamsUniqueChatsMessages {
//   return {
//     security_user_id: [],
//     id: item.id,
//     kind: "Chat",
//     replyToId: item.replyToId ? encrypt(item.replyToId) : null,
//     messageType: item.messageType ? encrypt(item.messageType) : null,
//     createdDateTime: item.createdDateTime ?? null,
//     lastModifiedDateTime: item.lastModifiedDateTime ?? null,
//     chatId: item.chatId ?? null,
//     from: item.from ? {
//       application: item.from.application ? {
//         displayName: encrypt(item.from.application.displayName)
//       } : null,
//       device: item.from.device ? {
//         displayName: encrypt(item.from.device.displayName)
//       } : null,
//       user: item.from.user ? {
//         displayName: encrypt(item.from.user.displayName)
//       } : null,

//     } : null,
//     body: item.body ? {
//       content: encrypt(item.body.content)
//     } : null,
//     hasAttachments: item.hasAttachments,
//     channelIdentity: item.channelIdentity ? {
//       teamId: item.channelIdentity.teamId,
//       channelId: encrypt(item.channelIdentity.channelId)
//     } : null,
//     softDelete: false,
//   };
// }

function createEncryptedMessage(item: ITeamsUniqueChatsMessages): ITeamsUniqueChatsMessages {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? item.replyToId : null,
    messageType: item.messageType ? item.messageType : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: item.from.application.displayName
      } : null,
      device: item.from.device ? {
        displayName: item.from.device.displayName
      } : null,
      user: item.from.user ? {
        displayName: item.from.user.displayName
      } : null,

    } : null,
    body: item.body ? {
      content: item.body.content
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: item.channelIdentity.channelId
    } : null,
    softDelete: false,
  };
}
/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processUserChatsMembers(
  logger: Logger,
  client: Client,
  uniqueChatsData: Array<{chatId: string, topic: string, chatType: string}>,
  userBatch: IBatchResponseData[],
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {

  logger.log(`[Impl:processUserChatsMembers] Total Unique Chats to Process: ${uniqueChatsData.length}`);
  // logger.log(`[Impl:processUserChatsMembers] uniqueChatsData: ${JSON.stringify(uniqueChatsData)}`);  // !!!
  // logger.log(`[Impl:processUserChatsMembers] userBatch: ${JSON.stringify(userBatch)}`);  // !!!

  const userChatsMembersBatchRequestsCreated: BatchRequestData[] = uniqueChatsData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createChatsMembersRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processUserChatsMembers] Created ${userChatsMembersBatchRequestsCreated.length} Chats Members Requests for ${userBatch.length} Users`);
 
  const splitChatsMembersBatchRequests = splitArrayIntoChunks(userChatsMembersBatchRequestsCreated, CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS);
  logger.log(`[Impl:processUserChatsMembers] Total Split Chats Members Batch: ${splitChatsMembersBatchRequests.length} | CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS: ${CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS}`);
  logger.log(`[Impl:processUserChatsMembers] splitChatsMembersBatchRequests: ${JSON.stringify(splitChatsMembersBatchRequests)}`); // !!!

  const totalChatsMembersBatchRequests = splitChatsMembersBatchRequests.length;
  for (let i = 0; i < totalChatsMembersBatchRequests; i++) {
    const currentChatsMembersBatchRequests = i + 1;
    logger.log(`\n----- 🚀 2. START BATCH = 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch}-----`);
    try {

      const currentSplitChatsMembersBatchRequests = splitChatsMembersBatchRequests[i];
      // logger.log(`[Impl:processUserChatsMembers] splitChatsMembersBatchRequests_inside == ${JSON.stringify(splitChatsMembersBatchRequests)}`); // !!!
      // logger.log(`[Impl:processUserChatsMembers] currentSplitChatsMembersBatchRequests == ${JSON.stringify(currentSplitChatsMembersBatchRequests)}`); // !!!

      const batchResultChatsMembers = await fetchJsonBatchForChats(logger, client, currentSplitChatsMembersBatchRequests);
      // // *** View Raw Response
      // logger.log(`*** [Impl:processUserChatsMembers] batchResultChatsMembers == ${JSON.stringify(batchResultChatsMembers)}`); // !!!

      if (!batchResultChatsMembers.responses || batchResultChatsMembers.responses.length === 0) {
        logger.log(`[Impl:processUserChatsBatch] No Responses in API REQUEST - CHATS MEMBERS`);
        continue;
      }
      // Create Filtered version of batchResultChatsMembers
      const filteredBatchResultChatsMembers = {
        responses: (batchResultChatsMembers.responses ?? []).map(response => {
          const filteredResponse = { ...response };
          // Compare the batchResultChatsMembers "Teams Members" with userBatch "M365 Members"
          if (filteredResponse.body?.value && Array.isArray(filteredResponse.body.value)) {
            const filteredMembers = filteredResponse.body.value.filter(member => 
              userBatch.some(user => user.id === (member as { userId: string }).userId)
            );
            const uniqueUserIds = new Set();
            const uniqueMembers = filteredMembers.filter(member => {
              const userId = (member as { userId: string }).userId;
              if (uniqueUserIds.has(userId)) {
                return false;
              } else {
                uniqueUserIds.add(userId);
                return true;
              }
            });
            filteredResponse.body = {
              ...filteredResponse.body,
              value: uniqueMembers
            };
          }
          return filteredResponse;
        })
      };
      // logger.log(`[Impl:processUserChatsMembers] *** filteredBatchResultChatsMembers == ${JSON.stringify(filteredBatchResultChatsMembers)}`); // !!!

      await processBatchResultChatsMembers(logger, filteredBatchResultChatsMembers, currentChatsMembersBatchRequests, totalChatsMembersBatchRequests);

      batchResultChatsMembers.responses = [];
      splitChatsMembersBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processUserChatsBatch] Error Processing API REQUEST - CHATS MEMBERS: ${i + 1}: ${error}`);
      continue;
    }
    logger.log(`----- 🚀 2. END BATCH = 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} | 🙍 USERS BATCH: ${currentUserBatchNumber} of ${totalUserBatch}-----`);
    global.gc && global.gc();
  }
}

async function processBatchResultChatsMembers(
  logger: Logger,
  filteredBatchResultChatsMembers: IBatchResponses,
  currentChatsMembersBatchRequests: number,
  totalChatsMembersBatchRequests: number
): Promise<void> {

  const totalBatchResultChatsMembers = filteredBatchResultChatsMembers.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChatsMembers; j++) {
    const currentBatchResultChatsMembers = j + 1;
    const batchResultChatsMembersResponses = (filteredBatchResultChatsMembers.responses ?? [])[j];
    const teamId = batchResultChatsMembersResponses.id;

    logger.log(`\n\n***** 🚀 3. START BATCH = 🔄 PROCESSSING RESULT - CHATS_MEMBERS: teamId: ${teamId} - ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} | 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} *****`);

    if (!batchResultChatsMembersResponses || batchResultChatsMembersResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChatsMembers] Skipping teamId: ${teamId} with Error Data: ${batchResultChatsMembersResponses?.status}`);
      continue;
    }

    const singleResultChatsMembers = { responses: [batchResultChatsMembersResponses] };
    // logger.info(`[Impl:processBatchResultChatsMembers] singleResultChatsMembers == ${JSON.stringify(singleResultChatsMembers)}`);  // !!!

    const modifiedChatsMembersChunk = processSingleResultChatsMembers([singleResultChatsMembers], logger);
    // *** View Raw Response
    // logger.info(`[Impl:processBatchResultChatsMembers] *** modifiedChatsMembersChunk == ${JSON.stringify(modifiedChatsMembersChunk)}`);  // !!!

    const totalModifiedChatsMembersChunk = modifiedChatsMembersChunk.length;

    // Updating Start...
    for (let k = 0; k < totalModifiedChatsMembersChunk; k++) {
      const membersChunk = modifiedChatsMembersChunk[k];
      const currentModifiedMembersChunk = k + 1;
      if (!membersChunk) {
        logger.info(`[Impl:processBatchResultChatsMembers] Skipping Undefined UniqueChatsMessages at Index: ${k} === (teamId: ${teamId} / Batch Result: ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} / Batch Request: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests})`);
        continue;
      }

      logger.log(`\n+++++ 🚀 4. START BATCH = ➕ UPDATING CHATS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunk} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} ChatsMembers Inside | 🔄 PROCESSSING RESULT - CHATS_MEMBERS: teamId: ${teamId} - ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} | 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} +++++`);
      try {
        logger.info(`[Impl:processBatchResultChatsMembers] Updating ChatsMembers...`);
        await updateChatsMembers(logger, [membersChunk]);
        logger.info(`[Impl:processBatchResultChatsMembers] Successfully Updated ChatsMembers...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChatsMembers] Failed Updating: ${error}`);
        continue;
      }
      modifiedChatsMembersChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ 🚀 4. END BATCH = ➕ UPDATING CHATS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunk} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} ChatsMembers Inside | 🔄 PROCESSSING RESULT - CHATS_MEMBERS: teamId: ${teamId} - ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} | 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} +++++`);
    }
    // Updating End...

    modifiedChatsMembersChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`***** 🚀 3. END BATCH = 🔄 PROCESSSING RESULT - CHATS_MEMBERS: teamId: ${teamId} - ${currentBatchResultChatsMembers} of ${totalBatchResultChatsMembers} | 📡 API REQUEST - CHATS MEMBERS: ${currentChatsMembersBatchRequests} of ${totalChatsMembersBatchRequests} *****`);
  }
}

function processSingleResultChatsMembers(
  singleResultChatsMembers: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChatsMembers) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChatsMembers] Skipping Invalid ChatsMembers Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalChatsMembers = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChatsMembers] Total ChatsMembers in response.body.value: ${totalChatsMembers}`);

      const teamsId = response.id ?? '';
      const allChatsMembers = response.body?.value as ITeamsChatsMembers[];
      // logger.log(`[Impl:processSingleResultChatsMembers] allChatsMembers: ${JSON.stringify(allChatsMembers)}`); // !!!
      const chunkSize = CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE;

      // Process members in chunks
      for (let i = 0; i < allChatsMembers.length; i += chunkSize) {
        const chatMembersAfterChunk = allChatsMembers.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultChatsMembers] chatMembersAfterChunk: ${JSON.stringify(chatMembersAfterChunk)}`); // !!!
        processUserChatsMembersChunk(teamsId, chatMembersAfterChunk, allProcessedData, logger);
        chatMembersAfterChunk.length = 0;
      }
      allChatsMembers.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultChatsMembers] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processUserChatsMembersChunk(
  teamsId: string,
  chatMembersAfterChunk: ITeamsChatsMembers[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChatsMembers[] = [];

  for (const item of chatMembersAfterChunk) {
    if (hasEmptyRequiredFieldsMembers(item, logger)) {
      continue;
    }
    processedValues.push(createFieldMembers(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);
    logger.info(`[Impl:processUserChatsMembersChunk] Successfully Modified ChatsMembers: ${processedValues.length} ChatsMembers Inside Chunk | CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE: ${CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMembers(item: ITeamsChatsMembers, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyId = !item.id || isEmpty(item.id);
  const hasEmptyValues = hasEmptyId;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFieldsMembers] Skipping message with empty required fields. Message ID: ${item.id}`);
    return true;
  }
  return false;
}

function createFieldMembers(item: ITeamsChatsMembers): ITeamsChatsMembers {
  return {
    id: item.id,
    displayName: item.displayName,
    visibleHistoryStartDateTime: item.visibleHistoryStartDateTime,
    userId: item.userId,
    email: item.email
  };
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/