import * as React from 'react';
import {
  <PERSON><PERSON>, <PERSON>er, Input, Loader, Tooltip,
} from '@fluentui/react-northstar';
import {
  AddIcon, AcceptIcon, CloseIcon, InfoIcon,
} from '@fluentui/react-icons-northstar';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
// import TeamsSettingTabs, { TeamsSettingTabType, TeamsSettingTabTypeValue }
//   from './TeamsSettingTabs';
import { TeamsSettingTabType, TeamsSettingTabTypeValue } from './TeamsSettingTabs';
import SelectedItemsList from './SelectedItemsList';
import { UseTeamsChatsApiReturnType } from '../../../../hooks/accessors/useTeamsChatsApiAccessor';
import MessageToaster, { ToasterMessage } from '../../../commons/molecules/message-toaster/MessageToaster';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';
import useTeamsSettingData from '../../../../hooks/features/useTeamsSettingData';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';

// CSS
import './TeamsSettingModal.scss';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType,
  eventReporter: EventReporter;
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
    eventReporter,
  } = props;

  const {
    // 新規登録、更新
    postTeamsChatsApi,
    // 同期
    getTeamsChatsApi,
    // 削除
    deleteTeamsChatsApi,
  } = useTeamsChatsApiAccessorReturn;

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
  };

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    fetchUserChatsAndChannels,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // IndexedDB features
  const [openDB] = useIndexedDbAccessor();

  // TeamsSettingDataフックを使用
  const {
    allChatItems,
    savedItems,
    isLoadingSavedItems,
    saveSelectedItems,
    setSavedItems,
  } = useTeamsSettingData({
    fetchUserChatsAndChannels,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen: open || false,
    openDB,
    eventReporter,
  });

  // 検索状態管理
  const [searchQuery, setSearchQuery] = React.useState('');
  // 検索後アイテム
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  // 選択されたアイテム
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  // タブ状態管理
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );
  // ページネーション状態
  const [currentPage, setCurrentPage] = React.useState(1);
  const ITEMS_PER_PAGE = 20;
  // 保存状態管理
  const [isSaving, setIsSaving] = React.useState(false);
  // 保存完了状態管理
  const [isSaveCompleted, setIsSaveCompleted] = React.useState(false);
  // トースター機能
  const [isToasterShown, toasterMessage, extendPopupTimer] = useMessageToasterBehavior(3000);
  // 選択上限数
  const MAX_SELECTION_COUNT = 10;
  // 統合されたローディング状態
  const isLoadingData = isLoading || isLoadingSavedItems;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    const hasSelectedItems = selectedItems.size > 0 ? 'has-selected-items' : '';
    return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
  }, [className, open, selectedItems.size]);

  // 保存済みアイテムを選択状態に反映するEffect
  React.useEffect(() => {
    setSelectedItems(new Set(savedItems));
  }, [savedItems]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allChatItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
    // 検索結果が変わったら1ページ目に戻る
    setCurrentPage(1);
  }, [searchQuery, allChatItems, activeTab]);

  // ページネーション用の計算
  const totalPages = Math.ceil(filteredChatItems.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentPageItems = filteredChatItems.slice(startIndex, endIndex);

  // ページ変更ハンドラー
  const handlePageChange = React.useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handleClose = React.useCallback(() => {
    // 状態をリセット
    setSearchQuery('');
    setSelectedItems(new Set());
    setSavedItems(new Set());
    setActiveTab(TeamsSettingTabType.CHAT);
    setCurrentPage(1);
    setIsSaving(false);
    setIsSaveCompleted(false);

    if (onClose) onClose();
  }, [onClose, setSavedItems]);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // タブ変更ハンドラー
  // const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
  //   setActiveTab(tab);
  //   // タブ切り替え時に検索クエリをクリア
  //   setSearchQuery('');
  // }, []);

  // チャットとチャネルの件数を計算
  // const chatCount = React.useMemo(() => allChatItems.filter((item) => item.type
  // === TeamsSettingTabType.CHAT).length, [allChatItems]);

  // const channelCount = React.useMemo(() => allChatItems.filter((item) => item.type
  // === TeamsSettingTabType.CHANNEL).length, [allChatItems]);

  // プレースホルダーテキストをタブに応じて変更
  // const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT
  //   ? 'チャット名で検索' : 'チャネル名で検索'), [activeTab]);
  const searchPlaceholder = 'チャット名で検索';

  // 保存ボタンのコンテンツ
  const saveButtonContent = React.useMemo(() => {
    if (isSaving) {
      return (
        <>
          <Loader size="smallest" inline />
          {' '}
          保存中...
        </>
      );
    }
    if (isSaveCompleted) {
      return '保存完了';
    }
    return '保存';
  }, [isSaving, isSaveCompleted]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        // 選択解除の場合
        newSet.delete(id);
      } else {
        // 選択追加の場合：上限チェック
        if (newSet.size >= MAX_SELECTION_COUNT) {
          // 上限に達している場合はトースターメッセージを表示
          extendPopupTimer(ToasterMessage.MAX_TEAMS_SELECTION);
          return prev; // 状態を変更しない
        }
        newSet.add(id);
      }
      return newSet;
    });
  }, [extendPopupTimer]);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(id);
    }
  }, [handleItemToggle]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    if (!saveSelectedItems) {
      throw new Error('保存機能が利用できません');
    }
    // ローディング状態を開始
    setIsSaving(true);
    try {
      // キュー処理を使用して保存
      await saveSelectedItems(selectedItems, allChatItems);
      // 保存完了状態を表示
      setIsSaving(false);
      setIsSaveCompleted(true);
      // 0.8秒後にモーダルを閉じる
      setTimeout(() => {
        handleClose();
        // 閉じる時間を管理
      }, 800);
    } finally {
      // ローディング状態を終了（保存完了時は上で既に設定済み）
      if (!isSaveCompleted) {
        setIsSaving(false);
      }
    }
  }, [
    saveSelectedItems,
    selectedItems,
    allChatItems,
    handleClose,
    isSaveCompleted]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={TeamsSettingLabel.TITLE} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <div className="simple-modal-description">
              <p>検索対象を選択できます。最大10件まで選択可能です。</p>
              <Tooltip
                content="チャット選択後、検索可能になるまで最大1時間程度お待ちください。"
                trigger={(
                  <Button
                    text
                    iconOnly
                    icon={<InfoIcon />}
                    className="teams-setting-info-button"
                    aria-label="データ取得に関する情報"
                  />
                )}
              />
            </div>
            {/* タブ切り替え */}
            {/* <TeamsSettingTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              disabled={isLoadingData}
              chatCount={chatCount}
              channelCount={channelCount}
            /> */}
            {/* 検索フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fluid
              />
            </div>
            {/* 選択されたアイテム一覧 */}
            <SelectedItemsList
              selectedItems={selectedItems}
              allChatItems={allChatItems}
              onRemoveItem={handleRemoveSelectedItem}
            />
            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoadingData && (
                <div className="simple-modal-loading">
                  <p>チャットを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoadingData && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoadingData && !error && currentPageItems.map((item) => {
                const isSelected = selectedItems.has(item.id);
                const itemClassName = `simple-modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item.id)}
                    onKeyDown={(event) => handleKeyDown(event, item.id)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="simple-modal-chat-item-content">
                      <span className="simple-modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
            {/* ページネーション */}
            {!isLoadingData && !error && totalPages > 1 && (
              <div className="simple-modal-pagination">
                <div className="pagination-info">
                  <span>
                    {filteredChatItems.length}
                    件中
                    {startIndex + 1}
                    -
                    {Math.min(endIndex, filteredChatItems.length)}
                    件を表示
                  </span>
                </div>
                <div className="pagination-controls">
                  <Button
                    content="前へ"
                    disabled={currentPage === 1}
                    onClick={() => handlePageChange(currentPage - 1)}
                    size="small"
                  />
                  <span className="pagination-current">
                    {currentPage}
                    {' '}
                    /
                    {totalPages}
                  </span>
                  <Button
                    content="次へ"
                    disabled={currentPage === totalPages}
                    onClick={() => handlePageChange(currentPage + 1)}
                    size="small"
                  />
                </div>
              </div>
            )}
            {/* 保存ボタン */}
            <div className="simple-modal-save-section" style={{ marginTop: '20px', padding: '0 20px' }}>
              <Button
                primary={!isSaveCompleted}
                content={saveButtonContent}
                disabled={
                  !postTeamsChatsApi
                  || !deleteTeamsChatsApi
                  || isSaving
                  || isSaveCompleted
                  || (selectedItems.size === 0 && savedItems.size === 0)
                  || (selectedItems.size === savedItems.size
                      && Array.from(selectedItems).every((id) => savedItems.has(id)))
                }
                onClick={handleSave}
                fluid
                styles={isSaveCompleted ? {
                  root: {
                    backgroundColor: '#107c10',
                    borderColor: '#107c10',
                    color: 'white',
                  },
                } : undefined}
              />
            </div>
          </div>
        </div>
      </div>
      {/* トースターメッセージ */}
      <MessageToaster
        isActive={isToasterShown}
        messageType={toasterMessage}
      />
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  open: false,
};

export default TeamsSettingModal;
